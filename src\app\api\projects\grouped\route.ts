import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

interface SeriesBookWithProject {
  book_number: number
  book_role: string
  projects: {
    id: string
    title: string
    description?: string
    status: string
    current_word_count: number
    target_word_count: number
    primary_genre?: string
    created_at: string
    updated_at: string
  } | null
}

interface SeriesWithBooks {
  id: string
  title: string
  description?: string
  genre?: string
  planned_book_count?: number
  current_book_count: number
  publication_status: string
  created_at: string
  series_books?: SeriesBookWithProject[]
}

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch all series with their books
    const { data: series, error: seriesError } = await supabase
      .from('series')
      .select(`
        id,
        title,
        description,
        genre,
        planned_book_count,
        current_book_count,
        publication_status,
        created_at,
        series_books (
          book_number,
          book_role,
          projects (
            id,
            title,
            description,
            status,
            current_word_count,
            target_word_count,
            primary_genre,
            created_at,
            updated_at
          )
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (seriesError) {
      console.error('Error fetching series:', seriesError)
      return NextResponse.json({ error: 'Failed to fetch series' }, { status: 500 })
    }

    // Fetch standalone projects (not in any series)
    // Note: We'll fetch all projects and filter out those in series

    // To get standalone projects, we need to fetch all projects and filter out those in series
    const { data: allProjects, error: allProjectsError } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (allProjectsError) {
      console.error('Error fetching projects:', allProjectsError)
      return NextResponse.json({ error: 'Failed to fetch projects' }, { status: 500 })
    }

    // Get all project IDs that are in series
    const projectsInSeries = new Set<string>()
    const typedSeries = series as SeriesWithBooks[] | null
    typedSeries?.forEach(s => {
      s.series_books?.forEach(sb => {
        if (sb.projects?.id) {
          projectsInSeries.add(sb.projects.id)
        }
      })
    })

    // Filter standalone projects
    const standalone = allProjects?.filter(p => !projectsInSeries.has(p.id)) || []

    // Transform series data for better structure
    const seriesWithBooks = typedSeries?.map(s => ({
      id: s.id,
      title: s.title,
      description: s.description,
      genre: s.genre,
      planned_book_count: s.planned_book_count,
      current_book_count: s.current_book_count,
      publication_status: s.publication_status,
      created_at: s.created_at,
      books: s.series_books
        ?.filter(sb => sb.projects)
        ?.map(sb => ({
          ...sb.projects,
          book_number: sb.book_number,
          book_role: sb.book_role,
        }))
        ?.sort((a, b) => a.book_number - b.book_number) || []
    })) || []

    // Calculate totals
    const totalSeries = seriesWithBooks.length
    const totalBooksInSeries = projectsInSeries.size
    const totalStandalone = standalone.length
    const totalProjects = totalBooksInSeries + totalStandalone

    return NextResponse.json({ 
      series: seriesWithBooks,
      standalone,
      stats: {
        totalSeries,
        totalBooksInSeries,
        totalStandalone,
        totalProjects,
      }
    })
  } catch (error) {
    console.error('Error in grouped projects GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}