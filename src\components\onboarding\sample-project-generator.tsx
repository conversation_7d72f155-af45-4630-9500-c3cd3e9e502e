'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { BookOpen, Sparkles, Clock, Target, AlertCircle } from 'lucide-react'

interface SampleProject {
  id: string
  title: string
  description: string
  genre: string
  wordCount: number
  estimatedTime: string
  features: string[]
  selections: Record<string, unknown>
}

const sampleProjects: SampleProject[] = [
  {
    id: 'fantasy-adventure',
    title: 'The Crystal of Aethermoor',
    description: 'A young apprentice discovers an ancient crystal that holds the power to save their dying kingdom from an eternal winter.',
    genre: 'Epic Fantasy',
    wordCount: 80000,
    estimatedTime: '5 minutes',
    features: ['Magic System', 'Character Arcs', 'World Building', 'Quest Structure'],
    selections: {
      primaryGenre: 'Fantasy',
      subgenre: 'Epic Fantasy',
      narrativeVoice: 'Third Person Limited',
      tense: 'Past',
      toneOptions: ['Adventurous', 'Heroic'],
      writingStyle: 'Descriptive',
      structureType: 'Three-Act Structure',
      pacingPreference: 'Moderate',
      chapterStructure: 'Traditional Chapters',
      timelineComplexity: 'Linear',
      protagonistTypes: ['Reluctant Hero'],
      antagonistTypes: ['Dark Lord'],
      characterComplexity: 'Moderate',
      characterArcTypes: ['Hero\'s Journey'],
      timePeriod: 'Medieval Fantasy',
      geographicSetting: 'Fictional World',
      worldType: 'Secondary World',
      magicTechLevel: 'High Magic',
      majorThemes: ['Good vs Evil', 'Coming of Age'],
      targetAudience: 'Young Adult',
      contentRating: 'PG-13',
      projectScope: 'Standalone Novel',
      targetWordCount: 80000,
      targetChapters: 20,
      povCharacterCount: 1,
      povCharacterType: 'Single POV'
    }
  },
  {
    id: 'sci-fi-thriller',
    title: 'Neural Echo',
    description: 'In 2087, a cybersecurity expert discovers their memories have been hacked, leading them down a dangerous path to uncover a conspiracy that reaches the highest levels of government.',
    genre: 'Cyberpunk Thriller',
    wordCount: 75000,
    estimatedTime: '4 minutes',
    features: ['Tech Noir', 'Mind-bending Plot', 'Dystopian Setting', 'Fast Pacing'],
    selections: {
      primaryGenre: 'Science Fiction',
      subgenre: 'Cyberpunk',
      narrativeVoice: 'First Person',
      tense: 'Present',
      toneOptions: ['Dark', 'Suspenseful'],
      writingStyle: 'Concise',
      structureType: 'Three-Act Structure',
      pacingPreference: 'Fast',
      chapterStructure: 'Short Chapters',
      timelineComplexity: 'Non-linear',
      protagonistTypes: ['Anti-hero'],
      antagonistTypes: ['Corporation'],
      characterComplexity: 'Complex',
      characterArcTypes: ['Redemption'],
      timePeriod: 'Near Future',
      geographicSetting: 'Urban',
      worldType: 'Alternate Earth',
      magicTechLevel: 'High Tech',
      majorThemes: ['Identity', 'Technology vs Humanity'],
      philosophicalThemes: ['Free Will', 'Reality vs Simulation'],
      targetAudience: 'Adult',
      contentRating: 'R',
      projectScope: 'Standalone Novel',
      targetWordCount: 75000,
      targetChapters: 18,
      povCharacterCount: 1,
      povCharacterType: 'Single POV'
    }
  },
  {
    id: 'mystery-cozy',
    title: 'Secrets of Millbrook Manor',
    description: 'When librarian Emma inherits her great-aunt\'s manor, she discovers a decades-old mystery involving missing heirloom recipes and a secret that could change everything.',
    genre: 'Cozy Mystery',
    wordCount: 60000,
    estimatedTime: '3 minutes',
    features: ['Small Town Setting', 'Amateur Sleuth', 'Light Romance', 'Food Theme'],
    selections: {
      primaryGenre: 'Mystery',
      subgenre: 'Cozy Mystery',
      narrativeVoice: 'Third Person Limited',
      tense: 'Past',
      toneOptions: ['Light-hearted', 'Cozy'],
      writingStyle: 'Conversational',
      structureType: 'Mystery Structure',
      pacingPreference: 'Gentle',
      chapterStructure: 'Traditional Chapters',
      timelineComplexity: 'Linear with Flashbacks',
      protagonistTypes: ['Amateur Sleuth'],
      antagonistTypes: ['Hidden Culprit'],
      characterComplexity: 'Moderate',
      characterArcTypes: ['Personal Growth'],
      timePeriod: 'Contemporary',
      geographicSetting: 'Small Town',
      worldType: 'Real World',
      magicTechLevel: 'Low Tech',
      majorThemes: ['Community', 'Family Secrets'],
      targetAudience: 'General Adult',
      contentRating: 'PG',
      projectScope: 'Standalone Novel',
      targetWordCount: 60000,
      targetChapters: 15,
      povCharacterCount: 1,
      povCharacterType: 'Single POV'
    }
  }
]

interface SampleProjectGeneratorProps {
  onProjectCreated?: (projectId: string) => void
}

export function SampleProjectGenerator({ onProjectCreated }: SampleProjectGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [selectedProject, setSelectedProject] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [progress, setProgress] = useState<string>('')
  const router = useRouter()
  const supabase = createClient()

  const handleGenerateSample = async (sampleProject: SampleProject) => {
    setIsGenerating(true)
    setSelectedProject(sampleProject.id)
    setError(null)
    setProgress('Initializing...')
    
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')

      setProgress('Creating project...')

      // Create the project with sample data
      const projectData = {
        user_id: user.id,
        title: sampleProject.title,
        description: sampleProject.description,
        status: 'planning',
        ...sampleProject.selections
      }

      const { data: project, error: projectError } = await supabase
        .from('projects')
        .insert([projectData])
        .select()
        .single()

      if (projectError) throw projectError

      setProgress('Adding story structure...')

      // Add sample story structure
      const sampleActs = [
        {
          project_id: project.id,
          structure_data: {
            actNumber: 1,
            description: 'Setup and inciting incident',
            keyEvents: ['Character introduction', 'Normal world establishment', 'Inciting incident', 'First plot point']
          },
          act_number: 1,
          description: 'Setup and inciting incident',
          key_events: ['Character introduction', 'Normal world establishment', 'Inciting incident', 'First plot point']
        },
        {
          project_id: project.id,
          structure_data: {
            actNumber: 2,
            description: 'Rising action and obstacles',
            keyEvents: ['Character development', 'Obstacles and conflicts', 'Midpoint reversal', 'Crisis moment']
          },
          act_number: 2,
          description: 'Rising action and obstacles',
          key_events: ['Character development', 'Obstacles and conflicts', 'Midpoint reversal', 'Crisis moment']
        },
        {
          project_id: project.id,
          structure_data: {
            actNumber: 3,
            description: 'Climax and resolution',
            keyEvents: ['Final confrontation', 'Climax', 'Resolution', 'New equilibrium']
          },
          act_number: 3,
          description: 'Climax and resolution',
          key_events: ['Final confrontation', 'Climax', 'Resolution', 'New equilibrium']
        }
      ]

      for (const act of sampleActs) {
        const { error: actError } = await supabase.from('story_arcs').insert(act)
        if (actError) {
          console.warn('Error creating story arc:', actError)
          // Continue with project creation even if some story arcs fail
        }
      }

      setProgress('Creating characters...')

      // Add sample characters
      const sampleCharacters = [
        {
          project_id: project.id,
          name: 'Protagonist',
          role: 'Main Character',
          description: 'The hero of our story',
          backstory: 'A brief backstory will be generated here',
          personality_traits: { traits: ['Brave', 'Determined', 'Curious'] },
          character_arc: { arc: 'Growth from reluctant to confident' }
        },
        {
          project_id: project.id,
          name: 'Antagonist',
          role: 'Villain',
          description: 'The primary opposition',
          backstory: 'A compelling villain backstory',
          personality_traits: { traits: ['Cunning', 'Powerful', 'Motivated'] },
          character_arc: { arc: 'Escalating threat to hero' }
        }
      ]

      for (const character of sampleCharacters) {
        const { error: characterError } = await supabase.from('characters').insert(character)
        if (characterError) {
          console.warn('Error creating character:', characterError)
          // Continue with project creation
        }
      }

      setProgress('Setting up chapters...')

      // Add sample chapter outlines
      const chaptersToCreate = Math.min(5, typeof sampleProject.selections.targetChapters === 'number' ? sampleProject.selections.targetChapters : 5)
      const targetWordCount = typeof sampleProject.selections.targetWordCount === 'number' ? sampleProject.selections.targetWordCount : 60000
      const targetChapters = typeof sampleProject.selections.targetChapters === 'number' ? sampleProject.selections.targetChapters : 15
      const wordsPerChapter = Math.floor(targetWordCount / targetChapters)

      for (let i = 1; i <= chaptersToCreate; i++) {
        const { error: chapterError } = await supabase.from('chapters').insert({
          project_id: project.id,
          chapter_number: i,
          title: `Chapter ${i}`,
          target_word_count: wordsPerChapter,
          outline: JSON.stringify({
            chapterNumber: i,
            title: `Chapter ${i}`,
            summary: `This chapter will advance the ${sampleProject.genre.toLowerCase()} plot and develop characters.`,
            keyEvents: [`Event ${i}A`, `Event ${i}B`],
            targetWordCount: wordsPerChapter,
            sampleProjectId: sampleProject.id
          }),
          status: 'planned'
        })
        
        if (chapterError) {
          console.warn(`Error creating chapter ${i}:`, chapterError)
          // Continue with remaining chapters
        }
      }

      setProgress('Finalizing...')

      // Add initial story bible entries
      const storyBibleEntries = [
        {
          project_id: project.id,
          entry_type: 'setting',
          entry_key: 'primary_setting',
          entry_data: {
            category: 'Setting',
            title: 'Primary Setting',
            content: `This ${sampleProject.genre} story takes place in a ${sampleProject.selections.geographicSetting || 'fictional'} setting.`,
            tags: [sampleProject.genre.toLowerCase(), 'setting']
          }
        },
        {
          project_id: project.id,
          entry_type: 'themes',
          entry_key: 'major_themes',
          entry_data: {
            category: 'Themes',
            title: 'Major Themes',
            content: `Key themes include: ${Array.isArray(sampleProject.selections.majorThemes) ? sampleProject.selections.majorThemes.join(', ') : 'Coming of age, Good vs Evil'}`,
            tags: ['themes', 'analysis']
          }
        }
      ]

      for (const entry of storyBibleEntries) {
        const { error: bibleError } = await supabase.from('story_bible').insert(entry)
        if (bibleError) {
          console.warn('Error creating story bible entry:', bibleError)
          // Continue - story bible is optional
        }
      }

      setProgress('Complete!')
      
      onProjectCreated?.(project.id)
      router.push(`/projects/${project.id}?sample=true`)
    } catch (error) {
      console.error('Error creating sample project:', error)

      // Better error handling for different error types
      let errorMessage = 'Unknown error occurred'

      if (error instanceof Error) {
        errorMessage = error.message
      } else if (typeof error === 'object' && error !== null) {
        // Handle Supabase errors
        if ('message' in error) {
          errorMessage = String(error.message)
        } else if ('details' in error) {
          errorMessage = String(error.details)
        } else if ('hint' in error) {
          errorMessage = String(error.hint)
        } else {
          errorMessage = JSON.stringify(error)
        }
      } else if (typeof error === 'string') {
        errorMessage = error
      }

      setError(`Failed to create sample project: ${errorMessage}`)
    } finally {
      setIsGenerating(false)
      setSelectedProject(null)
      setProgress('')
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Try a Sample Project</h2>
        <p className="text-muted-foreground">
          See BookScribe AI in action with a pre-built project structure. Choose a genre that interests you!
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {isGenerating && progress && (
        <Alert>
          <Sparkles className="h-4 w-4" />
          <AlertDescription>{progress}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
        {sampleProjects.map((project) => (
          <Card key={project.id} className="relative overflow-hidden">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{project.title}</CardTitle>
                  <Badge variant="secondary">{project.genre}</Badge>
                </div>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  {project.estimatedTime}
                </div>
              </div>
              <CardDescription className="text-sm">
                {project.description}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Target className="h-3 w-3" />
                  {project.wordCount.toLocaleString()} words
                </div>
                <div className="flex items-center gap-1">
                  <BookOpen className="h-3 w-3" />
                  {typeof project.selections.targetChapters === 'number' ? project.selections.targetChapters : 'N/A'} chapters
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium">Includes:</p>
                <div className="flex flex-wrap gap-1">
                  {project.features.map((feature, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>

              <Button 
                onClick={() => {
                  setError(null)
                  handleGenerateSample(project)
                }}
                disabled={isGenerating}
                className="w-full"
                variant={selectedProject === project.id ? "secondary" : "default"}
              >
                {isGenerating && selectedProject === project.id ? (
                  <>
                    <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Try This Sample
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-center">
        <p className="text-sm text-muted-foreground">
          Sample projects help you understand how the AI structures stories, creates characters, and plans chapters.
        </p>
      </div>
    </div>
  )
}