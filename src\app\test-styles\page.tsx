"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { ThemeToggle } from "@/components/ui/theme-toggle";

export default function TestStyles() {
  const [styles, setStyles] = useState<Record<string, string>>({});
  const [computedStyles, setComputedStyles] = useState<CSSStyleDeclaration | null>(null);

  useEffect(() => {
    const root = document.documentElement;
    const computed = window.getComputedStyle(root);
    setComputedStyles(computed);

    // Get all CSS custom properties
    const customProps: Record<string, string> = {};
    const allStyles = root.style;
    
    // Check inline styles
    for (let i = 0; i < allStyles.length; i++) {
      const prop = allStyles[i];
      if (prop && prop.startsWith('--')) {
        customProps[prop] = allStyles.getPropertyValue(prop);
      }
    }

    // Check computed styles for CSS variables
    const cssVars = [
      '--background',
      '--foreground',
      '--primary',
      '--primary-foreground',
      '--literary-gold',
      '--literary-amber',
      '--settings-ui-font',
      '--settings-ui-font-size',
      '--settings-reading-font',
      '--settings-editor-font'
    ];

    cssVars.forEach(varName => {
      const value = computed.getPropertyValue(varName);
      if (value) {
        customProps[varName] = value;
      }
    });

    setStyles(customProps);
  }, []);

  return (
    <div className="min-h-screen p-8 bg-background text-foreground">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="flex items-center justify-between">
          <h1 className="text-4xl font-bold font-literary-display">Style Diagnostics</h1>
          <ThemeToggle />
        </div>

        <section className="space-y-4">
          <h2 className="text-2xl font-semibold font-literary">Font Tests</h2>
          
          <div className="space-y-2">
            <p className="font-literary">Font Literary: The quick brown fox jumps over the lazy dog</p>
            <p className="font-literary-display">Font Literary Display: The quick brown fox jumps over the lazy dog</p>
            <p className="font-sans">Font Sans: The quick brown fox jumps over the lazy dog</p>
            <p className="font-mono">Font Mono: The quick brown fox jumps over the lazy dog</p>
            <p className="font-ui">Font UI: The quick brown fox jumps over the lazy dog</p>
            <p className="font-reading">Font Reading: The quick brown fox jumps over the lazy dog</p>
            <p className="font-editor">Font Editor: The quick brown fox jumps over the lazy dog</p>
          </div>
        </section>

        <section className="space-y-4">
          <h2 className="text-2xl font-semibold font-literary">Color Tests</h2>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 bg-primary text-primary-foreground rounded">Primary</div>
            <div className="p-4 bg-secondary text-secondary-foreground rounded">Secondary</div>
            <div className="p-4 bg-accent text-accent-foreground rounded">Accent</div>
            <div className="p-4 bg-muted text-muted-foreground rounded">Muted</div>
            <div className="p-4 bg-card text-card-foreground rounded border">Card</div>
            <div className="p-4 bg-destructive text-destructive-foreground rounded">Destructive</div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 rounded border-2 border-literary-gold">Literary Gold</div>
            <div className="p-4 rounded border-2 border-literary-amber">Literary Amber</div>
            <div className="p-4 bg-literary-parchment rounded">Literary Parchment</div>
            <div className="p-4 text-literary-ink">Literary Ink</div>
          </div>
        </section>

        <section className="space-y-4">
          <h2 className="text-2xl font-semibold font-literary">Button Variants</h2>
          
          <div className="flex flex-wrap gap-4">
            <Button variant="default">Default Button</Button>
            <Button variant="secondary">Secondary Button</Button>
            <Button variant="outline">Outline Button</Button>
            <Button variant="ghost">Ghost Button</Button>
            <Button variant="link">Link Button</Button>
            <Button variant="literary">Literary Button</Button>
            <Button variant="destructive">Destructive Button</Button>
          </div>
        </section>

        <section className="space-y-4">
          <h2 className="text-2xl font-semibold font-literary">CSS Variables</h2>
          
          <div className="bg-card p-4 rounded border overflow-x-auto">
            <pre className="text-xs font-mono">
              {JSON.stringify(styles, null, 2)}
            </pre>
          </div>
        </section>

        <section className="space-y-4">
          <h2 className="text-2xl font-semibold font-literary">Document Classes</h2>
          
          <div className="bg-card p-4 rounded border">
            <p className="font-mono text-sm">
              HTML classes: {document.documentElement.className || 'none'}
            </p>
            <p className="font-mono text-sm">
              Body classes: {document.body.className || 'none'}
            </p>
            <p className="font-mono text-sm">
              Theme attribute: {document.documentElement.getAttribute('data-theme') || 'none'}
            </p>
          </div>
        </section>

        <section className="space-y-4">
          <h2 className="text-2xl font-semibold font-literary">Computed Styles</h2>
          
          <div className="bg-card p-4 rounded border space-y-2 text-sm font-mono">
            <p>Font Family (body): {computedStyles?.fontFamily || 'loading...'}</p>
            <p>Font Size (body): {computedStyles?.fontSize || 'loading...'}</p>
            <p>Background Color: {computedStyles?.backgroundColor || 'loading...'}</p>
            <p>Color: {computedStyles?.color || 'loading...'}</p>
          </div>
        </section>
      </div>
    </div>
  );
}