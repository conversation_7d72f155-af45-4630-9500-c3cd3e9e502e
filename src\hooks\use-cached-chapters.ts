'use client'

import { useEffect, useCallback } from 'react'
import { useCachedData, chapterCache, chapterListCache, cacheKeys, invalidateChapterCache } from '@/lib/cache/client'
import { createClient } from '@/lib/supabase/client'
import type { Database } from '@/lib/db/types'
import type { RealtimePostgresChangesPayload } from '@supabase/supabase-js'

type Chapter = Database['public']['Tables']['chapters']['Row']

interface UseCachedChaptersOptions {
  projectId: string
  enabled?: boolean
}

export function useCachedChapters({ projectId, enabled = true }: UseCachedChaptersOptions) {
  const supabase = createClient()
  
  const fetcher = async () => {
    const { data, error } = await supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .order('chapter_number')
    
    if (error) throw error
    return data as Chapter[]
  }
  
  const result = useCachedData<Chapter[]>({
    cacheKey: cacheKeys.chapterList(projectId),
    cache: chapterListCache,
    fetcher,
    dependencies: [projectId],
    staleTime: 30000, // Consider data stale after 30 seconds
  })

  const handleCacheInvalidation = useCallback((payload: RealtimePostgresChangesPayload<Chapter>) => {
    // Invalidate cache and refresh
    if (payload.eventType === 'DELETE' && payload.old) {
      const oldChapter = payload.old as Chapter
      if (oldChapter.id) {
        invalidateChapterCache(oldChapter.id, projectId)
      }
    } else if (payload.new) {
      const newChapter = payload.new as Chapter
      if (newChapter.id) {
        invalidateChapterCache(newChapter.id, projectId)
      }
    }
    result.refresh()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId])

  // Subscribe to real-time updates
  useEffect(() => {
    if (!enabled) return

    const subscription = supabase
      .channel(`chapters-${projectId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'chapters',
          filter: `project_id=eq.${projectId}`,
        },
        handleCacheInvalidation
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [projectId, enabled, handleCacheInvalidation, supabase])

  return result
}

export function useCachedChapter(chapterId: string | null) {
  const supabase = createClient()
  
  const fetcher = async (): Promise<Chapter | { __null: true }> => {
    if (!chapterId) return { __null: true }

    const { data, error } = await supabase
      .from('chapters')
      .select('*')
      .eq('id', chapterId)
      .single()

    if (error) throw error
    return data as Chapter
  }
  
  const result = useCachedData<Chapter | { __null: true }>({
    cacheKey: chapterId ? cacheKeys.chapter(chapterId) : 'no-chapter',
    cache: chapterCache,
    fetcher,
    dependencies: [chapterId],
    staleTime: 30000,
  })

  const handleChapterInvalidation = useCallback(() => {
    if (chapterId) {
      // Invalidate cache and refresh
      invalidateChapterCache(chapterId)
      result.refresh()
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chapterId])

  // Subscribe to real-time updates for this specific chapter
  useEffect(() => {
    if (!chapterId) return

    const subscription = supabase
      .channel(`chapter-${chapterId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'chapters',
          filter: `id=eq.${chapterId}`,
        },
        handleChapterInvalidation
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [chapterId, handleChapterInvalidation, supabase])

  return result
}