import OpenAI from 'openai'
import { createClient } from '@/lib/supabase/server'
import { config } from '@/lib/config'

export interface StoryGenerationRequest {
  projectId: string
  action: 'generate_complete_story' | 'generate_structure' | 'generate_characters' | 'generate_chapters' | 'write_chapter'
  chapterNumber?: number
  options?: {
    includeCharacters?: boolean
    includeWorldBuilding?: boolean
    includeStoryBible?: boolean
    regenerateExisting?: boolean
  }
}

export interface GeneratedStoryStructure {
  title: string
  premise: string
  genre: string
  themes: string[]
  acts: Array<{
    number: number
    title: string
    description: string
    chapters: number[]
    keyEvents: string[]
    wordCountTarget: number
  }>
  plotPoints: Array<{
    type: 'inciting_incident' | 'plot_point_1' | 'midpoint' | 'plot_point_2' | 'climax' | 'resolution'
    chapter: number
    description: string
  }>
  worldBuilding: {
    setting: string
    rules: string[]
    locations: Array<{ name: string; description: string }>
  }
}

export interface GeneratedCharacter {
  name: string
  role: 'protagonist' | 'antagonist' | 'supporting' | 'minor'
  description: string
  personality: {
    traits: string[]
    motivations: string[]
    fears: string[]
    goals: string[]
  }
  background: string
  characterArc: string
  relationships: Array<{ character: string; relationship: string }>
}

export interface GeneratedChapter {
  chapterNumber: number
  title: string
  summary: string
  scenes: Array<{
    number: number
    setting: string
    characters: string[]
    purpose: string
    conflict: string
    outcome: string
  }>
  keyEvents: string[]
  characterDevelopment: string[]
  plotAdvancement: string
  wordCountTarget: number
}

export class ComprehensiveStoryGenerator {
  private openai: OpenAI
  private supabase: ReturnType<typeof createClient>

  constructor() {
    this.openai = new OpenAI({ apiKey: config.openai.apiKey })
  }

  async initialize() {
    this.supabase = await createClient()
  }

  async generateCompleteStory(request: StoryGenerationRequest): Promise<{
    structure: GeneratedStoryStructure
    characters: GeneratedCharacter[]
    chapters: GeneratedChapter[]
    storyBible: any
  }> {
    await this.initialize()

    // Get project details
    const { data: project } = await this.supabase
      .from('projects')
      .select('*')
      .eq('id', request.projectId)
      .single()

    if (!project) {
      throw new Error('Project not found')
    }

    console.log('Generating complete story for project:', project.title)

    // Generate story structure
    const structure = await this.generateStoryStructure(project)
    
    // Generate characters
    const characters = await this.generateCharacters(project, structure)
    
    // Generate chapter outlines
    const chapters = await this.generateChapterOutlines(project, structure, characters)
    
    // Generate story bible
    const storyBible = await this.generateStoryBible(project, structure, characters)

    // Save everything to database
    await this.saveToDatabase(request.projectId, structure, characters, chapters, storyBible)

    return {
      structure,
      characters,
      chapters,
      storyBible
    }
  }

  private async generateStoryStructure(project: any): Promise<GeneratedStoryStructure> {
    const prompt = `
Create a comprehensive story structure for a ${project.primary_genre} novel titled "${project.title}".

Project Description: ${project.description || 'A compelling story'}
Target Word Count: ${project.target_word_count || 80000}
Target Chapters: ${project.target_chapters || 20}
Narrative Voice: ${project.narrative_voice || 'Third person'}
Tone: ${project.tone_options?.join(', ') || 'Engaging'}

Generate a detailed story structure including:
1. Refined title and premise
2. Core themes (3-5)
3. Three-act structure with specific chapters
4. Key plot points with chapter locations
5. World building elements
6. Setting and locations

Format as JSON with this structure:
{
  "title": "string",
  "premise": "string", 
  "genre": "string",
  "themes": ["string"],
  "acts": [{"number": 1, "title": "string", "description": "string", "chapters": [1,2,3], "keyEvents": ["string"], "wordCountTarget": 26000}],
  "plotPoints": [{"type": "inciting_incident", "chapter": 2, "description": "string"}],
  "worldBuilding": {"setting": "string", "rules": ["string"], "locations": [{"name": "string", "description": "string"}]}
}
`

    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.8,
      max_tokens: 2000
    })

    const content = response.choices[0]?.message?.content
    if (!content) throw new Error('Failed to generate story structure')

    try {
      return JSON.parse(content)
    } catch (error) {
      console.error('Failed to parse story structure JSON:', content)
      throw new Error('Invalid story structure format')
    }
  }

  private async generateCharacters(project: any, structure: GeneratedStoryStructure): Promise<GeneratedCharacter[]> {
    const prompt = `
Create detailed characters for the ${structure.genre} novel "${structure.title}".

Story Premise: ${structure.premise}
Themes: ${structure.themes.join(', ')}
Setting: ${structure.worldBuilding.setting}

Generate 4-6 main characters including:
- 1 protagonist
- 1 main antagonist  
- 2-4 supporting characters

For each character include: name, role, description, personality (traits, motivations, fears, goals), background, character arc, and key relationships.

Format as JSON array of character objects.
`

    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.8,
      max_tokens: 2500
    })

    const content = response.choices[0]?.message?.content
    if (!content) throw new Error('Failed to generate characters')

    try {
      return JSON.parse(content)
    } catch (error) {
      console.error('Failed to parse characters JSON:', content)
      throw new Error('Invalid characters format')
    }
  }

  private async generateChapterOutlines(
    project: any, 
    structure: GeneratedStoryStructure, 
    characters: GeneratedCharacter[]
  ): Promise<GeneratedChapter[]> {
    const chapters: GeneratedChapter[] = []
    const targetChapters = project.target_chapters || 20
    const wordsPerChapter = Math.floor((project.target_word_count || 80000) / targetChapters)

    for (let i = 1; i <= targetChapters; i++) {
      const chapter = await this.generateSingleChapterOutline(i, structure, characters, wordsPerChapter)
      chapters.push(chapter)
    }

    return chapters
  }

  private async generateSingleChapterOutline(
    chapterNumber: number,
    structure: GeneratedStoryStructure,
    characters: GeneratedCharacter[],
    wordCountTarget: number
  ): Promise<GeneratedChapter> {
    // Find which act this chapter belongs to
    const act = structure.acts.find(act => act.chapters.includes(chapterNumber)) || structure.acts[0]
    
    // Find relevant plot points
    const plotPoint = structure.plotPoints.find(pp => pp.chapter === chapterNumber)

    const prompt = `
Create a detailed outline for Chapter ${chapterNumber} of "${structure.title}".

Act: ${act.title} - ${act.description}
${plotPoint ? `Plot Point: ${plotPoint.type} - ${plotPoint.description}` : ''}

Available Characters: ${characters.map(c => `${c.name} (${c.role})`).join(', ')}
Target Word Count: ${wordCountTarget}

Generate:
1. Chapter title
2. Brief summary
3. 2-4 scenes with settings, characters, purpose, conflict, and outcome
4. Key events that happen
5. Character development moments
6. How this chapter advances the plot

Format as JSON.
`

    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
      max_tokens: 1000
    })

    const content = response.choices[0]?.message?.content
    if (!content) throw new Error(`Failed to generate chapter ${chapterNumber} outline`)

    try {
      const parsed = JSON.parse(content)
      return {
        chapterNumber,
        wordCountTarget,
        ...parsed
      }
    } catch (error) {
      console.error(`Failed to parse chapter ${chapterNumber} JSON:`, content)
      // Return a basic structure if parsing fails
      return {
        chapterNumber,
        title: `Chapter ${chapterNumber}`,
        summary: `Chapter ${chapterNumber} continues the story.`,
        scenes: [],
        keyEvents: [],
        characterDevelopment: [],
        plotAdvancement: `Chapter ${chapterNumber} advances the plot.`,
        wordCountTarget
      }
    }
  }

  private async generateStoryBible(
    project: any,
    structure: GeneratedStoryStructure,
    characters: GeneratedCharacter[]
  ): Promise<any> {
    return {
      worldBuilding: structure.worldBuilding,
      characterProfiles: characters,
      themes: structure.themes,
      timeline: structure.plotPoints,
      rules: structure.worldBuilding.rules,
      locations: structure.worldBuilding.locations
    }
  }

  private async saveToDatabase(
    projectId: string,
    structure: GeneratedStoryStructure,
    characters: GeneratedCharacter[],
    chapters: GeneratedChapter[],
    storyBible: any
  ) {
    // Save story arcs (acts)
    for (const act of structure.acts) {
      await this.supabase.from('story_arcs').upsert({
        project_id: projectId,
        act_number: act.number,
        description: act.description,
        key_events: act.keyEvents
      })
    }

    // Save characters
    for (const character of characters) {
      await this.supabase.from('characters').upsert({
        project_id: projectId,
        name: character.name,
        role: character.role,
        description: character.description,
        personality_traits: character.personality,
        character_arc: character.characterArc
      })
    }

    // Save chapters
    for (const chapter of chapters) {
      await this.supabase.from('chapters').upsert({
        project_id: projectId,
        chapter_number: chapter.chapterNumber,
        title: chapter.title,
        target_word_count: chapter.wordCountTarget,
        outline: JSON.stringify(chapter),
        status: 'planned'
      })
    }

    // Save story bible entries
    const storyBibleEntries = [
      { entry_type: 'world_building', entry_key: 'setting', entry_data: structure.worldBuilding },
      { entry_type: 'themes', entry_key: 'main_themes', entry_data: { themes: structure.themes } },
      { entry_type: 'plot_structure', entry_key: 'acts', entry_data: { acts: structure.acts } }
    ]

    for (const entry of storyBibleEntries) {
      await this.supabase.from('story_bible').upsert({
        project_id: projectId,
        ...entry
      })
    }
  }
}
