import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const envInfo = {
      NODE_ENV: process.env.NODE_ENV,
      NEXT_PUBLIC_DEMO_MODE: process.env.NEXT_PUBLIC_DEMO_MODE,
      NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'SET' : 'NOT_SET',
      NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'SET' : 'NOT_SET',
      SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'SET' : 'NOT_SET',
      OPENAI_API_KEY: process.env.OPENAI_API_KEY ? 'SET' : 'NOT_SET',
      STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY ? 'SET' : 'NOT_SET',
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ? 'SET' : 'NOT_SET',
      STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET ? 'SET' : 'NOT_SET',
      STRIPE_PRICE_ID_BASIC: process.env.STRIPE_PRICE_ID_BASIC ? 'SET' : 'NOT_SET',
      STRIPE_PRICE_ID_PRO: process.env.STRIPE_PRICE_ID_PRO ? 'SET' : 'NOT_SET',
      STRIPE_PRICE_ID_ENTERPRISE: process.env.STRIPE_PRICE_ID_ENTERPRISE ? 'SET' : 'NOT_SET',
    }

    // Check demo mode detection logic
    const isDemoMode = process.env.NEXT_PUBLIC_DEMO_MODE === 'true' || 
                      (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_SUPABASE_URL);

    return NextResponse.json({ 
      envInfo,
      isDemoMode,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Debug env error:', error)
    return NextResponse.json({ 
      error: 'Debug env failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
