// Temporarily disable <PERSON><PERSON> to debug
// import * as Sentry from '@sentry/nextjs';

export async function register() {
  console.log('Instrumentation register called');
  // Temporarily disable Sentry imports
  // if (process.env.NEXT_RUNTIME === 'nodejs') {
  //   await import('../sentry.server.config');
  // }

  // if (process.env.NEXT_RUNTIME === 'edge') {
  //   await import('../sentry.edge.config');
  // }
  console.log('Instrumentation register completed');
}

// export const onRequestError = Sentry.captureRequestError;
