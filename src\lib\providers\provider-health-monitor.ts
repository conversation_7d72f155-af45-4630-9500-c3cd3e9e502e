interface ProviderHealth {
  name: string
  status: 'healthy' | 'degraded' | 'failed'
  lastCheck: Date
  errorCount: number
  loadTime?: number
  memoryUsage?: number
}

interface HealthMetrics {
  totalProviders: number
  healthyProviders: number
  degradedProviders: number
  failedProviders: number
  averageLoadTime: number
  totalErrors: number
}

export class ProviderHealthMonitor {
  private static instance: ProviderHealthMonitor
  private providers: Map<string, ProviderHealth> = new Map()
  private metrics: HealthMetrics = {
    totalProviders: 0,
    healthyProviders: 0,
    degradedProviders: 0,
    failedProviders: 0,
    averageLoadTime: 0,
    totalErrors: 0
  }

  static getInstance(): ProviderHealthMonitor {
    if (!ProviderHealthMonitor.instance) {
      ProviderHealthMonitor.instance = new ProviderHealthMonitor()
    }
    return ProviderHealthMonitor.instance
  }

  registerProvider(name: string): void {
    this.providers.set(name, {
      name,
      status: 'healthy',
      lastCheck: new Date(),
      errorCount: 0
    })
    this.updateMetrics()
  }

  recordProviderLoad(name: string, loadTime: number): void {
    const provider = this.providers.get(name)
    if (provider) {
      provider.loadTime = loadTime
      provider.lastCheck = new Date()
      
      // Determine status based on load time
      if (loadTime > 5000) {
        provider.status = 'degraded'
      } else if (loadTime > 10000) {
        provider.status = 'failed'
      } else {
        provider.status = 'healthy'
      }
      
      this.updateMetrics()
    }
  }

  recordProviderError(name: string, error: Error): void {
    const provider = this.providers.get(name)
    if (provider) {
      provider.errorCount++
      provider.lastCheck = new Date()
      
      // Update status based on error count
      if (provider.errorCount >= 3) {
        provider.status = 'failed'
      } else if (provider.errorCount >= 1) {
        provider.status = 'degraded'
      }
      
      this.updateMetrics()
      
      // Log error for debugging
      console.error(`Provider ${name} error:`, error)
      
      // Report to monitoring service
      if (typeof window !== 'undefined' && (window as any).Sentry) {
        (window as any).Sentry.captureException(error, {
          tags: {
            provider: name,
            errorCount: provider.errorCount
          }
        })
      }
    }
  }

  getProviderHealth(name: string): ProviderHealth | undefined {
    return this.providers.get(name)
  }

  getAllProviderHealth(): ProviderHealth[] {
    return Array.from(this.providers.values())
  }

  getMetrics(): HealthMetrics {
    return { ...this.metrics }
  }

  private updateMetrics(): void {
    const providers = Array.from(this.providers.values())
    
    this.metrics.totalProviders = providers.length
    this.metrics.healthyProviders = providers.filter(p => p.status === 'healthy').length
    this.metrics.degradedProviders = providers.filter(p => p.status === 'degraded').length
    this.metrics.failedProviders = providers.filter(p => p.status === 'failed').length
    this.metrics.totalErrors = providers.reduce((sum, p) => sum + p.errorCount, 0)
    
    const loadTimes = providers.filter(p => p.loadTime).map(p => p.loadTime!)
    this.metrics.averageLoadTime = loadTimes.length > 0 
      ? loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length 
      : 0
  }

  // Health check for critical providers
  async performHealthCheck(): Promise<boolean> {
    const criticalProviders = ['theme', 'notifications']
    const healthyCount = criticalProviders.filter(name => {
      const provider = this.providers.get(name)
      return provider && provider.status === 'healthy'
    }).length

    return healthyCount === criticalProviders.length
  }

  // Get recommendations based on health status
  getRecommendations(): string[] {
    const recommendations: string[] = []
    const metrics = this.getMetrics()

    if (metrics.failedProviders > 0) {
      recommendations.push('Some providers have failed. Consider reloading the page.')
    }

    if (metrics.degradedProviders > 0) {
      recommendations.push('Some providers are running slowly. Check your network connection.')
    }

    if (metrics.averageLoadTime > 3000) {
      recommendations.push('Providers are loading slowly. Consider enabling performance mode.')
    }

    if (metrics.totalErrors > 5) {
      recommendations.push('Multiple provider errors detected. Consider reporting this issue.')
    }

    return recommendations
  }

  // Reset provider health (useful for recovery)
  resetProviderHealth(name: string): void {
    const provider = this.providers.get(name)
    if (provider) {
      provider.status = 'healthy'
      provider.errorCount = 0
      provider.lastCheck = new Date()
      this.updateMetrics()
    }
  }

  // Export health data for debugging
  exportHealthData(): string {
    return JSON.stringify({
      providers: Array.from(this.providers.entries()),
      metrics: this.metrics,
      timestamp: new Date().toISOString()
    }, null, 2)
  }
}
