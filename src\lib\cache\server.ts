import { LRUCache } from 'lru-cache'
import type { Database } from '@/lib/db/types'

type Chapter = Database['public']['Tables']['chapters']['Row']
type Character = Database['public']['Tables']['characters']['Row']
type StoryArc = Database['public']['Tables']['story_arcs']['Row']

interface StoryBibleData {
  characters: Character[]
  storyArcs: StoryArc[]
}

// Cache configuration types
interface CacheOptions {
  max?: number // Maximum number of items
  ttl?: number // Time to live in milliseconds
  updateAgeOnGet?: boolean // Reset TTL on access
  updateAgeOnHas?: boolean // Reset TTL on has() check
}

// Generic cache wrapper
export class Cache<T extends {} = any> {
  private cache: LRUCache<string, T>
  
  constructor(options: CacheOptions = {}) {
    this.cache = new LRUCache({
      max: options.max || 100,
      ttl: options.ttl || 1000 * 60 * 5, // 5 minutes default
      updateAgeOnGet: options.updateAgeOnGet ?? true,
      updateAgeOnHas: options.updateAgeOnHas ?? false,
    })
  }
  
  get(key: string): T | undefined {
    return this.cache.get(key)
  }
  
  set(key: string, value: T): void {
    this.cache.set(key, value)
  }
  
  has(key: string): boolean {
    return this.cache.has(key)
  }
  
  delete(key: string): boolean {
    return this.cache.delete(key)
  }
  
  clear(): void {
    this.cache.clear()
  }
  
  size(): number {
    return this.cache.size
  }
}

// Specific cache instances for different use cases
interface ProjectData {
  id: string;
  name: string;
  [key: string]: unknown;
}



interface CharacterData {
  id: string;
  name: string;
  [key: string]: unknown;
}

interface AIResponseData {
  response: string;
  [key: string]: unknown;
}

interface SearchResultData {
  results: unknown[];
  [key: string]: unknown;
}

export const projectCache = new Cache<ProjectData>({
  max: 50,
  ttl: 1000 * 60 * 10, // 10 minutes
})

export const projectListCache = new Cache<any>({
  max: 50,
  ttl: 1000 * 60 * 10, // 10 minutes
})

export const chapterCache = new Cache<Chapter | { __null: true }>({
  max: 100,
  ttl: 1000 * 60 * 5, // 5 minutes
})

export const chapterListCache = new Cache<Chapter[]>({
  max: 50,
  ttl: 1000 * 60 * 5, // 5 minutes
})

export const storyBibleCache = new Cache<StoryBibleData>({
  max: 50,
  ttl: 1000 * 60 * 5, // 5 minutes
})

export const characterCache = new Cache<CharacterData>({
  max: 200,
  ttl: 1000 * 60 * 15, // 15 minutes
})

export const characterListCache = new Cache<Character[]>({
  max: 50,
  ttl: 1000 * 60 * 5, // 5 minutes
})

export const aiResponseCache = new Cache<AIResponseData>({
  max: 50,
  ttl: 1000 * 60 * 30, // 30 minutes
})

export const searchResultsCache = new Cache<SearchResultData>({
  max: 100,
  ttl: 1000 * 60 * 10, // 10 minutes
})

// Cache key generators
export const cacheKeys = {
  project: (projectId: string) => `project:${projectId}`,
  projectList: (userId: string) => `projects:${userId}`,
  chapter: (chapterId: string) => `chapter:${chapterId}`,
  chapterList: (projectId: string) => `chapters:${projectId}`,
  character: (characterId: string) => `character:${characterId}`,
  characterList: (projectId: string) => `characters:${projectId}`,
  storyBible: (projectId: string) => `story-bible:${projectId}`,
  aiSuggestion: (projectId: string, chapterId: string, type: string) => 
    `ai:${projectId}:${chapterId}:${type}`,
  search: (query: string, filters: string) => `search:${query}:${filters}`,
  analytics: (projectId: string, metric: string) => `analytics:${projectId}:${metric}`,
}

// Cache invalidation helpers
export const invalidateProjectCache = (projectId: string) => {
  projectCache.delete(cacheKeys.project(projectId))
  // Also invalidate related caches
  chapterListCache.delete(cacheKeys.chapterList(projectId))
  characterListCache.delete(cacheKeys.characterList(projectId))
}

export const invalidateChapterCache = (chapterId: string, projectId?: string) => {
  chapterCache.delete(cacheKeys.chapter(chapterId))
  if (projectId) {
    chapterListCache.delete(cacheKeys.chapterList(projectId))
  }
}

export const invalidateCharacterCache = (characterId: string, projectId?: string) => {
  characterCache.delete(cacheKeys.character(characterId))
  if (projectId) {
    characterListCache.delete(cacheKeys.characterList(projectId))
  }
}

// Server-side cache for API routes
const serverCache = new Map<string, { data: unknown; expires: number }>()

export function getServerCache(key: string): unknown | null {
  const cached = serverCache.get(key)
  if (!cached) return null
  
  if (Date.now() > cached.expires) {
    serverCache.delete(key)
    return null
  }
  
  return cached.data
}

export function setServerCache(key: string, data: unknown, ttl: number = 60000): void {
  serverCache.set(key, {
    data,
    expires: Date.now() + ttl,
  })
}

// Cleanup old entries periodically
if (typeof window === 'undefined') {
  setInterval(() => {
    const now = Date.now()
    for (const [key, value] of serverCache.entries()) {
      if (now > value.expires) {
        serverCache.delete(key)
      }
    }
  }, 60000) // Clean up every minute
}