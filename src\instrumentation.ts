// Temporarily disable <PERSON><PERSON> to debug the issue
// import * as Sen<PERSON> from '@sentry/nextjs';

export async function register() {
  console.log('Instrumentation register called');
  try {
    // Temporarily disable Sentry imports
    // if (process.env.NEXT_RUNTIME === 'nodejs') {
    //   await import('../sentry.server.config');
    // }

    // if (process.env.NEXT_RUNTIME === 'edge') {
    //   await import('../sentry.edge.config');
    // }
    console.log('Instrumentation register completed successfully');
  } catch (error) {
    console.error('Instrumentation register error:', error);
    throw error;
  }
}

// export const onRequestError = Sentry.captureRequestError;
