'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  providerName?: string
}

interface State {
  hasError: boolean
  error?: Error
}

export class ProviderErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error(`Provider Error (${this.props.providerName || 'Unknown'}):`, error, errorInfo)
    
    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo)
    
    // Report to error tracking service
    if (typeof window !== 'undefined' && (window as { Sentry?: { captureException: (error: Error, context: object) => void } }).Sentry) {
      (window as { Sentry: { captureException: (error: Error, context: object) => void } }).Sentry.captureException(error, {
        contexts: {
          provider: {
            name: this.props.providerName || 'Unknown',
            errorInfo: errorInfo.componentStack
          }
        }
      })
    }
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default fallback UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-background">
          <div className="text-center p-8 max-w-md">
            <h2 className="text-2xl font-bold text-destructive mb-4">
              Provider Error
            </h2>
            <p className="text-muted-foreground mb-4">
              {this.props.providerName || 'A provider'} failed to load. 
              The application will continue with limited functionality.
            </p>
            <button
              onClick={() => this.setState({ hasError: false, error: undefined })}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            >
              Retry
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// Hook version for functional components
export function useProviderErrorHandler(providerName: string) {
  return React.useCallback((error: Error, errorInfo: ErrorInfo) => {
    console.error(`Provider Error (${providerName}):`, error, errorInfo)
    
    if (typeof window !== 'undefined' && (window as { Sentry?: { captureException: (error: Error, context: object) => void } }).Sentry) {
      (window as { Sentry: { captureException: (error: Error, context: object) => void } }).Sentry.captureException(error, {
        contexts: {
          provider: { name: providerName, errorInfo: errorInfo.componentStack }
        }
      })
    }
  }, [providerName])
}
