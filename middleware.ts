import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import { config as appConfig } from '@/lib/config'

export async function middleware(request: NextRequest) {
  // Temporarily disable middleware to debug 500 errors
  return NextResponse.next();

  try {
    // Skip middleware for Next.js internal routes
    if (request.nextUrl.pathname.startsWith('/_next')) {
      return NextResponse.next()
    }

    // Handle CORS for API routes
    if (request.nextUrl.pathname.startsWith('/api')) {
      // Get origin from request
      const origin = request.headers.get('origin') || ''

      // Define allowed origins
      const allowedOrigins = [
        appConfig.app.url,
        'http://localhost:3000',
        'http://localhost:3001',
      ];
      
      // Check if origin is allowed (strict check even in development)
      const isAllowedOrigin = allowedOrigins.includes(origin);
      
      // Handle preflight requests
      if (request.method === 'OPTIONS') {
        return new NextResponse(null, {
          status: 200,
          headers: {
            'Access-Control-Allow-Origin': isAllowedOrigin ? origin : appConfig.app.url,
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
            'Access-Control-Max-Age': '86400',
            'Access-Control-Allow-Credentials': 'true',
          },
        });
      }
      
      // For actual requests, add CORS headers to the response
      const response = NextResponse.next();
      
      if (isAllowedOrigin) {
        response.headers.set('Access-Control-Allow-Origin', origin);
        response.headers.set('Access-Control-Allow-Credentials', 'true');
      }
      
      return response;
    }

    // In development, add extra safety checks
    if (process.env.NODE_ENV === 'development') {
      // Non-API routes continue as normal
      return NextResponse.next()
    }

    // Check if Supabase environment variables are available
    if (!appConfig.supabase.url || !appConfig.supabase.anonKey) {
      return NextResponse.next()
    }

    // Import the update session function dynamically to avoid initialization issues
    const { updateSession } = await import('@/lib/supabase/middleware')
    return await updateSession(request)
  } catch (error) {
    // If there's an error, continue without auth to prevent app crash
    return NextResponse.next()
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)  
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api routes (they have their own auth)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|api|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}