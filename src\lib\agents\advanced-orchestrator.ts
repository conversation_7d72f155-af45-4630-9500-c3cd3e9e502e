import { EventEmitter } from 'events';
import { StoryArchitectAgent } from './story-architect';
import { CharacterDeveloperAgent } from './character-developer';
import { ChapterPlannerAgent } from './chapter-planner';
import { WritingAgent } from './writing-agent';
import { ContentGenerator } from '../services/content-generator';
import { AdaptivePlanningAgent } from './adaptive-planning-agent';
import { 
  BookContext, 
  AgentResponse,
  StoryStructure,
  CharacterProfiles,
  ChapterOutlines
} from './types';
import type { ProjectSettings } from '@/lib/types/project-settings';

export interface TaskDefinition {
  id: string;
  type: 'story_analysis' | 'character_development' | 'chapter_planning' | 'content_generation' | 'quality_check';
  dependencies: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedDuration: number; // in seconds
  agent: string;
  payload: Record<string, unknown>;
  retryCount?: number;
  maxRetries?: number;
}

export interface TaskResult {
  taskId: string;
  success: boolean;
  result?: Record<string, unknown>;
  error?: string;
  duration: number;
  agentUsed: string;
}

export interface OrchestrationProgress {
  totalTasks: number;
  completedTasks: number;
  runningTasks: number;
  failedTasks: number;
  estimatedTimeRemaining: number;
  currentPhase: string;
}

export class AdvancedAgentOrchestrator extends EventEmitter {
  private storyArchitect!: StoryArchitectAgent;
  private characterDeveloper!: CharacterDeveloperAgent;
  private chapterPlanner!: ChapterPlannerAgent;
  private writingAgent!: WritingAgent;
  private contentGenerator = new ContentGenerator();
  private adaptivePlanner!: AdaptivePlanningAgent;
  private context!: BookContext;

  private maxConcurrentTasks = 3;
  private runningTasks = new Map<string, Promise<TaskResult>>();
  private completedTasks = new Map<string, TaskResult>();
  private failedTasks = new Map<string, TaskResult>();
  private taskQueue: TaskDefinition[] = [];
  private isPaused = false;

  constructor(maxConcurrentTasks = 3) {
    super();
    this.maxConcurrentTasks = maxConcurrentTasks;
  }

  private initializeAgents(projectId: string, settings: ProjectSettings) {
    // Create BookContext for agents
    this.context = {
      projectId,
      settings,
      projectSelections: settings
    };
    
    // Initialize agents
    this.storyArchitect = new StoryArchitectAgent();
    this.characterDeveloper = new CharacterDeveloperAgent();
    this.chapterPlanner = new ChapterPlannerAgent();
    this.adaptivePlanner = new AdaptivePlanningAgent(this.context);
    this.writingAgent = new WritingAgent(this.context);
  }

  async orchestrateProject(
    projectId: string,
    projectSelections: ProjectSettings,
    storyPrompt: string,
    targetWordCount: number,
    targetChapters: number
  ): Promise<AgentResponse<BookContext>> {
    const startTime = Date.now();
    
    try {
      // Initialize agents with project context
      this.initializeAgents(projectId, projectSelections);
      
      // Update context with additional properties
      Object.assign(this.context, {
        storyPrompt,
        targetWordCount,
        targetChapters,
        storyStructure: null,
        characters: [],
        chapterOutlines: [],
        completedChapters: [],
        storyBible: {
          worldRules: {},
          timeline: [],
          characterStates: {},
          plotThreads: []
        },
        metadata: {
          totalWordCount: 0,
          chaptersCompleted: 0,
          lastUpdated: new Date().toISOString()
        }
      });

      // Define task pipeline with dependencies
      const tasks = this.createTaskPipeline(this.context);
      
      // Execute tasks with parallel processing
      const results = await this.executeTaskPipeline(tasks);
      
      // Validate and finalize context
      const finalContext = await this.finalizeContext(results);

      return {
        success: true,
        data: finalContext,
        executionTime: Date.now() - startTime,
        tokensUsed: results.length
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown orchestration error'
      };
    }
  }

  private createTaskPipeline(context: BookContext): TaskDefinition[] {
    const tasks: TaskDefinition[] = [
      // Phase 1: Foundation (can run in parallel)
      {
        id: 'story_structure',
        type: 'story_analysis',
        dependencies: [],
        priority: 'critical',
        estimatedDuration: 120,
        agent: 'story_architect',
        payload: {
          projectSelections: context.projectSelections,
          storyPrompt: context.storyPrompt,
          targetWordCount: context.targetWordCount
        }
      },
      {
        id: 'world_building',
        type: 'story_analysis',
        dependencies: [],
        priority: 'high',
        estimatedDuration: 90,
        agent: 'content_generator',
        payload: {
          type: 'world_building',
          context: context.projectSelections,
          storyPrompt: context.storyPrompt
        }
      },
      {
        id: 'theme_analysis',
        type: 'story_analysis',
        dependencies: [],
        priority: 'medium',
        estimatedDuration: 60,
        agent: 'content_generator',
        payload: {
          type: 'theme_analysis',
          themes: context.projectSelections?.majorThemes || [],
          genre: context.projectSelections?.primaryGenre || ''
        }
      },

      // Phase 2: Character Development (depends on story structure)
      {
        id: 'main_characters',
        type: 'character_development',
        dependencies: ['story_structure'],
        priority: 'critical',
        estimatedDuration: 150,
        agent: 'character_developer',
        payload: {
          characterTypes: context.projectSelections?.protagonistTypes || [],
          storyContext: 'story_structure_result',
          count: 3
        }
      },
      {
        id: 'supporting_characters',
        type: 'character_development',
        dependencies: ['story_structure', 'main_characters'],
        priority: 'high',
        estimatedDuration: 100,
        agent: 'character_developer',
        payload: {
          characterTypes: context.projectSelections?.antagonistTypes || [],
          storyContext: 'story_structure_result',
          mainCharacters: 'main_characters_result',
          count: 5
        }
      },
      {
        id: 'character_relationships',
        type: 'character_development',
        dependencies: ['main_characters', 'supporting_characters'],
        priority: 'medium',
        estimatedDuration: 80,
        agent: 'content_generator',
        payload: {
          type: 'relationship_mapping',
          characters: ['main_characters_result', 'supporting_characters_result']
        }
      },

      // Phase 3: Chapter Planning (depends on story and characters)
      {
        id: 'chapter_structure',
        type: 'chapter_planning',
        dependencies: ['story_structure', 'main_characters'],
        priority: 'critical',
        estimatedDuration: 180,
        agent: 'chapter_planner',
        payload: {
          storyStructure: 'story_structure_result',
          characters: 'main_characters_result',
          targetChapters: context.targetChapters,
          targetWordCount: context.targetWordCount
        }
      },
      {
        id: 'scene_planning',
        type: 'chapter_planning',
        dependencies: ['chapter_structure', 'supporting_characters'],
        priority: 'high',
        estimatedDuration: 120,
        agent: 'content_generator',
        payload: {
          type: 'scene_planning',
          chapters: 'chapter_structure_result',
          allCharacters: ['main_characters_result', 'supporting_characters_result']
        }
      },

      // Phase 4: Quality Assurance (can run partially in parallel)
      {
        id: 'consistency_check',
        type: 'quality_check',
        dependencies: ['story_structure', 'main_characters', 'chapter_structure'],
        priority: 'high',
        estimatedDuration: 90,
        agent: 'adaptive_planner',
        payload: {
          type: 'consistency_validation',
          storyStructure: 'story_structure_result',
          characters: 'main_characters_result',
          chapters: 'chapter_structure_result'
        }
      },
      {
        id: 'pacing_analysis',
        type: 'quality_check',
        dependencies: ['chapter_structure', 'scene_planning'],
        priority: 'medium',
        estimatedDuration: 60,
        agent: 'content_generator',
        payload: {
          type: 'pacing_analysis',
          chapters: 'chapter_structure_result',
          scenes: 'scene_planning_result',
          pacingPreference: context.projectSelections?.pacingPreference || 'medium'
        }
      }
    ];

    return tasks.sort((a, b) => {
      // Sort by priority and dependencies
      const priorityWeight = { critical: 4, high: 3, medium: 2, low: 1 };
      return priorityWeight[b.priority] - priorityWeight[a.priority];
    });
  }

  private async executeTaskPipeline(tasks: TaskDefinition[]): Promise<TaskResult[]> {
    this.taskQueue = [...tasks];
    const results: TaskResult[] = [];

    this.emit('orchestration:started', {
      totalTasks: tasks.length,
      maxConcurrentTasks: this.maxConcurrentTasks
    });

    while (this.taskQueue.length > 0 || this.runningTasks.size > 0) {
      // If paused, wait for running tasks to complete but don't start new ones
      if (this.isPaused) {
        if (this.runningTasks.size > 0) {
          const completedTask = await this.waitForAnyTask();
          results.push(completedTask);
          this.emit('orchestration:progress', this.getProgress());
          
          if (!completedTask.success) {
            await this.handleTaskFailure(completedTask);
          }
        } else {
          // No running tasks and paused, emit paused event and wait
          this.emit('orchestration:paused');
          await new Promise(resolve => {
            const resumeHandler = () => {
              resolve(void 0);
              this.off('orchestration:resumed', resumeHandler);
            };
            this.on('orchestration:resumed', resumeHandler);
          });
        }
        continue;
      }
      
      // Start new tasks if we have capacity and ready tasks
      while (this.runningTasks.size < this.maxConcurrentTasks && this.taskQueue.length > 0 && !this.isPaused) {
        const readyTask = this.findReadyTask();
        if (readyTask) {
          this.startTask(readyTask);
        } else {
          break; // No ready tasks, wait for some to complete
        }
      }

      // Wait for at least one task to complete
      if (this.runningTasks.size > 0) {
        const completedTask = await this.waitForAnyTask();
        results.push(completedTask);
        
        this.emit('orchestration:progress', this.getProgress());
        
        // Handle failed tasks
        if (!completedTask.success) {
          await this.handleTaskFailure(completedTask);
        }
      }
    }

    this.emit('orchestration:completed', {
      totalTasks: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length
    });

    return results;
  }

  private findReadyTask(): TaskDefinition | null {
    for (let i = 0; i < this.taskQueue.length; i++) {
      const task = this.taskQueue[i];
      if (!task) continue;
      
      const dependenciesMet = task.dependencies.every(dep => 
        this.completedTasks.has(dep) && this.completedTasks.get(dep)!.success
      );
      
      if (dependenciesMet) {
        const removed = this.taskQueue.splice(i, 1);
        return removed.length > 0 ? removed[0] || null : null;
      }
    }
    return null;
  }

  private async startTask(task: TaskDefinition): Promise<void> {
    const taskPromise = this.executeTask(task);
    this.runningTasks.set(task.id, taskPromise);
    
    this.emit('task:started', {
      taskId: task.id,
      type: task.type,
      agent: task.agent,
      estimatedDuration: task.estimatedDuration
    });
  }

  private async executeTask(task: TaskDefinition): Promise<TaskResult> {
    const startTime = Date.now();
    
    try {
      // Resolve dependencies
      const resolvedPayload = this.resolveDependencies(task.payload, task.dependencies);
      
      // Execute based on agent type
      let result: StoryStructure | CharacterProfiles | ChapterOutlines | Record<string, unknown>;
      switch (task.agent) {
        case 'story_architect':
          const storyResponse = await this.storyArchitect.generateStoryStructure({
            projectSelections: this.context.projectSelections!,
            storyPrompt: this.context.storyPrompt!,
            targetWordCount: this.context.targetWordCount!,
            targetChapters: this.context.targetChapters!
          });
          if (!storyResponse.success || !storyResponse.data) {
            throw new Error(storyResponse.error || 'No story structure data');
          }
          result = storyResponse.data as StoryStructure;
          break;
        case 'character_developer':
          const charResponse = await this.characterDeveloper.generateCharacters({
            storyStructure: this.context.storyStructure!,
            projectSelections: this.context.projectSelections!,
            characterRequirements: resolvedPayload.characterRequirements as { protagonistCount: number; antagonistCount: number; supportingCount: number } || {
              protagonistCount: 1,
              antagonistCount: 1,
              supportingCount: 3
            }
          });
          if (!charResponse.success || !charResponse.data) {
            throw new Error(charResponse.error || 'No character data');
          }
          result = charResponse.data as CharacterProfiles;
          break;
        case 'chapter_planner':
          const chapterResponse = await this.chapterPlanner.generateChapterOutlines({
            storyStructure: this.context.storyStructure!,
            characters: this.context.characters as CharacterProfiles,
            targetWordCount: this.context.targetWordCount!,
            targetChapters: this.context.targetChapters!,
            projectSelections: this.context.projectSelections!
          });
          if (!chapterResponse.success || !chapterResponse.data) {
            throw new Error(chapterResponse.error || 'No chapter data');
          }
          result = chapterResponse.data as ChapterOutlines;
          break;
        case 'writing_agent':
          result = await this.writingAgent.execute();
          break;
        case 'content_generator':
          const contentRequest = {
            type: resolvedPayload.type as 'scene' | 'dialogue' | 'description' | 'chapter' | 'character' | 'plot-outline',
            prompt: resolvedPayload.prompt as string,
            context: resolvedPayload.context as Record<string, unknown> | undefined,
            style: resolvedPayload.style as string | undefined,
            length: resolvedPayload.length as 'short' | 'medium' | 'long' | undefined,
            tone: resolvedPayload.tone as string | undefined,
            projectId: this.context.projectId
          };
          const contentResponse = await this.contentGenerator.generateContent(contentRequest);
          result = contentResponse as Record<string, unknown>;
          break;
        case 'adaptive_planner':
          result = await this.adaptivePlanner.execute();
          break;
        default:
          throw new Error(`Unknown agent: ${task.agent}`);
      }

      const taskResult: TaskResult = {
        taskId: task.id,
        success: true,
        result: result as Record<string, unknown>,
        duration: Date.now() - startTime,
        agentUsed: task.agent
      };

      this.completedTasks.set(task.id, taskResult);
      return taskResult;

    } catch (error) {
      const taskResult: TaskResult = {
        taskId: task.id,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown task error',
        duration: Date.now() - startTime,
        agentUsed: task.agent
      };

      this.failedTasks.set(task.id, taskResult);
      return taskResult;
    }
  }

  private resolveDependencies(payload: Record<string, unknown>, dependencies: string[]): Record<string, unknown> {
    const resolved = { ...payload };
    
    for (const [key, value] of Object.entries(payload)) {
      if (typeof value === 'string' && value.endsWith('_result')) {
        const depId = value.replace('_result', '');
        if (dependencies.includes(depId)) {
          const depResult = this.completedTasks.get(depId);
          if (depResult && depResult.success) {
            resolved[key] = depResult.result;
          }
        }
      }
    }
    
    return resolved;
  }

  private async waitForAnyTask(): Promise<TaskResult> {
    const promises = Array.from(this.runningTasks.entries()).map(
      async ([taskId, promise]) => {
        const result = await promise;
        this.runningTasks.delete(taskId);
        return result;
      }
    );

    return Promise.race(promises);
  }

  private async handleTaskFailure(failedTask: TaskResult): Promise<void> {
    this.emit('task:failed', {
      taskId: failedTask.taskId,
      error: failedTask.error,
      duration: failedTask.duration
    });

    // Find the original task definition for retry logic
    const originalTask = this.taskQueue.find(t => t.id === failedTask.taskId);
    if (originalTask) {
      const retryCount = (originalTask.retryCount || 0) + 1;
      const maxRetries = originalTask.maxRetries || 2;

      if (retryCount <= maxRetries) {
        this.emit('task:retry', {
          taskId: failedTask.taskId,
          retryCount,
          maxRetries
        });

        // Add back to queue with updated retry count
        originalTask.retryCount = retryCount;
        this.taskQueue.unshift(originalTask);
      } else {
        this.emit('task:abandoned', {
          taskId: failedTask.taskId,
          retryCount,
          finalError: failedTask.error
        });
      }
    }
  }

  private getProgress(): OrchestrationProgress {
    const total = this.completedTasks.size + this.failedTasks.size + this.runningTasks.size + this.taskQueue.length;
    const completed = this.completedTasks.size;
    const running = this.runningTasks.size;
    const failed = this.failedTasks.size;

    // Estimate remaining time based on average task duration
    const completedResults = Array.from(this.completedTasks.values());
    const avgDuration = completedResults.length > 0
      ? completedResults.reduce((sum, r) => sum + r.duration, 0) / completedResults.length
      : 60000; // Default 1 minute

    const remainingTasks = this.taskQueue.length;
    const estimatedTimeRemaining = remainingTasks * (avgDuration / this.maxConcurrentTasks);

    return {
      totalTasks: total,
      completedTasks: completed,
      runningTasks: running,
      failedTasks: failed,
      estimatedTimeRemaining,
      currentPhase: this.getCurrentPhase()
    };
  }

  private getCurrentPhase(): string {
    const runningTaskTypes = Array.from(this.runningTasks.keys()).map(id => {
      const task = this.taskQueue.find(t => t.id === id);
      return task?.type || 'unknown';
    });

    if (runningTaskTypes.includes('story_analysis')) return 'Story Analysis';
    if (runningTaskTypes.includes('character_development')) return 'Character Development';
    if (runningTaskTypes.includes('chapter_planning')) return 'Chapter Planning';
    if (runningTaskTypes.includes('content_generation')) return 'Content Generation';
    if (runningTaskTypes.includes('quality_check')) return 'Quality Assurance';
    
    return 'Processing';
  }

  private async finalizeContext(results: TaskResult[]): Promise<BookContext> {
    if (!this.context) {
      throw new Error('Context not initialized');
    }

    // Consolidate results into final context
    for (const result of results.filter(r => r.success)) {
      switch (result.taskId) {
        case 'story_structure':
          this.context.storyStructure = result.result as unknown as StoryStructure;
          break;
        case 'main_characters':
        case 'supporting_characters':
          if (!this.context.characters) {
            this.context.characters = { protagonists: [], antagonists: [], supporting: [], relationships: [] };
          }
          // Handle character result appropriately
          break;
        case 'chapter_structure':
          this.context.chapterOutlines = {
            chapters: [],
            totalWordCount: 0,
            estimatedReadingTime: 0
          };
          break;
        case 'world_building':
          if (!this.context.storyBible) {
            this.context.storyBible = {
              projectId: this.context.projectId,
              lastUpdated: new Date(),
              structure: {} as StoryStructure,
              characters: {} as CharacterProfiles,
              world: {} as Record<string, unknown>,
              timeline: [],
              themes: [],
              continuity: {} as Record<string, unknown>,
              style: {} as Record<string, unknown>
            };
          }
          break;
        case 'character_relationships':
          // Handle relationships
          break;
      }
    }

    // Initialize memory system with the final context
    // Note: BookContextLoader doesn't have initializeFromBookContext method
    // The context is already loaded via loadProjectContext

    // Update metadata
    this.context.metadata = {
      totalWordCount: this.context.targetWordCount || 0,
      chaptersCompleted: 0,
      lastUpdated: new Date().toISOString()
    };

    return this.context;
  }

  // Public method to get real-time progress
  public getOrchestrationStatus(): OrchestrationProgress {
    return this.getProgress();
  }

  // Public method to cancel orchestration
  public async cancelOrchestration(): Promise<void> {
    this.taskQueue = [];
    this.emit('orchestration:cancelled');
  }

  // Public method to pause orchestration
  public pauseOrchestration(): void {
    this.isPaused = true;
    this.emit('orchestration:pause-requested');
  }

  // Public method to resume orchestration
  public resumeOrchestration(): void {
    this.isPaused = false;
    this.emit('orchestration:resumed');
  }

  // Public method to check if orchestration is paused
  public isOrchestrationPaused(): boolean {
    return this.isPaused;
  }
}