import OpenAI from 'openai';
import { BaseService } from './base-service';
import { WritingTask, ServiceResponse } from './types';
import { config } from '@/lib/config';

interface GenerationResult {
  content?: string;
  metadata?: Record<string, unknown>;
  quality?: number;
}

export class ContentGenerator extends BaseService {
  private openai: OpenAI;
  private generationQueue: WritingTask[] = [];
  private activeGenerations: Map<string, { task: WritingTask; startTime: number }> = new Map();

  constructor() {
    super({
      name: 'content-generator',
      version: '1.0.0',
      status: 'inactive',
      endpoints: ['/api/content/generate', '/api/content/templates'],
      dependencies: ['ai-orchestrator'],
      healthCheck: '/api/content/health'
    });

    this.openai = new OpenAI({
      apiKey: config.openai.apiKey,
    });
  }

  async initialize(): Promise<void> {
    this.startGenerationProcessor();
    this.isInitialized = true;
    this.setStatus('active');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string; uptime: number }>> {
    return this.createResponse(true, {
      status: `${this.generationQueue.length} queued, ${this.activeGenerations.size} processing`,
      uptime: Date.now() - (this.isInitialized ? Date.now() - 1000 : Date.now()),
    });
  }

  async shutdown(): Promise<void> {
    this.generationQueue = [];
    this.activeGenerations.clear();
    this.setStatus('inactive');
  }

  async generateContent(request: {
    type: 'scene' | 'dialogue' | 'description' | 'chapter' | 'character' | 'plot-outline';
    prompt: string;
    context?: Record<string, unknown>;
    style?: string;
    length?: 'short' | 'medium' | 'long';
    tone?: string;
    projectId: string;
  }): Promise<ServiceResponse<string>> {
    return this.withErrorHandling(async () => {
      const task: WritingTask = {
        id: `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        projectId: request.projectId,
        type: 'generate',
        priority: 'medium',
        input: {
          prompt: request.prompt,
          context: {
            type: request.type,
            style: request.style,
            length: request.length,
            tone: request.tone,
            ...request.context
          }
        },
        status: 'pending',
        createdAt: Date.now()
      };

      this.generationQueue.push(task);
      
      // Wait for completion
      return new Promise((resolve, reject) => {
        const checkCompletion = () => {
          if (task.status === 'completed' && task.output) {
            resolve(task.output.content);
          } else if (task.status === 'failed') {
            reject(new Error('Generation failed'));
          } else {
            setTimeout(checkCompletion, 500);
          }
        };
        
        setTimeout(checkCompletion, 100);
        
        // Timeout after 5 minutes for complex generation tasks
        setTimeout(() => {
          if (task.status === 'processing' || task.status === 'pending') {
            reject(new Error('Generation timeout'));
          }
        }, 300000);
      });
    });
  }

  async generateSceneOutline(request: {
    sceneGoal: string;
    characters: string[];
    setting: string;
    previousEvents: string;
    projectId: string;
  }): Promise<ServiceResponse<{
    outline: string;
    keyEvents: string[];
    conflicts: string[];
    emotionalBeats: string[];
  }>> {
    return this.withErrorHandling(async () => {
      const prompt = `
Create a detailed scene outline with the following parameters:

Scene Goal: ${request.sceneGoal}
Characters Present: ${request.characters.join(', ')}
Setting: ${request.setting}
Previous Events: ${request.previousEvents}

Provide:
1. A structured scene outline (3-5 key beats)
2. List of key events that must happen
3. Potential conflicts or tensions
4. Emotional beats for character development

Format as JSON with keys: outline, keyEvents, conflicts, emotionalBeats
      `;

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert story architect. Create detailed, engaging scene outlines that drive plot and character development forward.'
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 1500,
      });

      const content = completion.choices[0]?.message?.content || '{}';
      
      try {
        return JSON.parse(content);
      } catch {
        return {
          outline: content,
          keyEvents: [],
          conflicts: [],
          emotionalBeats: []
        };
      }
    });
  }

  async generateDialogue(request: {
    characters: { name: string; personality: string; goal: string }[];
    context: string;
    tone: string;
    length: number;
    projectId: string;
  }): Promise<ServiceResponse<{
    dialogue: { speaker: string; line: string; emotion?: string }[];
    tags: string[];
    subtext: string[];
  }>> {
    return this.withErrorHandling(async () => {
      const characterDescriptions = request.characters
        .map(char => `${char.name}: ${char.personality} (Goal: ${char.goal})`)
        .join('\n');

      const prompt = `
Write a dialogue scene between these characters:
${characterDescriptions}

Context: ${request.context}
Tone: ${request.tone}
Target length: Approximately ${request.length} lines

Requirements:
- Each character should speak in their distinct voice
- Include subtle subtext and character motivations
- Show personality through speech patterns
- Create natural flow and realistic interruptions

Format as JSON with:
{
  "dialogue": [{"speaker": "Name", "line": "dialogue text", "emotion": "optional"}],
  "tags": ["relevant", "story", "tags"],
  "subtext": ["underlying meanings", "character motivations"]
}
      `;

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert dialogue writer. Create realistic, character-driven dialogue that advances plot and reveals personality.'
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.8,
        max_tokens: 2000,
      });

      const content = completion.choices[0]?.message?.content || '{}';
      
      try {
        return JSON.parse(content);
      } catch {
        return {
          dialogue: [{ speaker: 'Unknown', line: content }],
          tags: [],
          subtext: []
        };
      }
    });
  }

  async generateCharacterProfile(request: {
    name: string;
    role: 'protagonist' | 'antagonist' | 'supporting';
    age?: number;
    background?: string;
    goals?: string[];
    conflicts?: string[];
    projectId: string;
  }): Promise<ServiceResponse<{
    profile: {
      name: string;
      age: number;
      appearance: string;
      personality: string;
      background: string;
      motivations: string[];
      fears: string[];
      strengths: string[];
      weaknesses: string[];
      relationships: { character: string; relationship: string }[];
      arc: string;
      voice: string;
    };
  }>> {
    return this.withErrorHandling(async () => {
      const prompt = `
Create a comprehensive character profile for:

Name: ${request.name}
Role: ${request.role}
${request.age ? `Age: ${request.age}` : ''}
${request.background ? `Background: ${request.background}` : ''}
${request.goals ? `Goals: ${request.goals.join(', ')}` : ''}
${request.conflicts ? `Conflicts: ${request.conflicts.join(', ')}` : ''}

Generate a detailed character profile including:
- Physical appearance
- Personality traits and quirks
- Background and history
- Core motivations and goals
- Deepest fears
- Key strengths and weaknesses
- Important relationships
- Character arc potential
- Distinctive voice/speech patterns

Format as JSON matching the profile structure.
      `;

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert character developer. Create rich, complex characters with depth, flaws, and compelling motivations.'
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 2500,
      });

      const content = completion.choices[0]?.message?.content || '{}';
      
      try {
        const profile = JSON.parse(content);
        return { profile };
      } catch {
        return {
          profile: {
            name: request.name,
            age: request.age || 25,
            appearance: 'To be determined',
            personality: content.substring(0, 200),
            background: request.background || 'To be determined',
            motivations: request.goals || [],
            fears: [],
            strengths: [],
            weaknesses: [],
            relationships: [],
            arc: 'To be determined',
            voice: 'To be determined'
          }
        };
      }
    });
  }

  async generateWorldBuilding(request: {
    type: 'location' | 'culture' | 'history' | 'magic-system' | 'technology';
    name: string;
    description: string;
    genre: string;
    projectId: string;
  }): Promise<ServiceResponse<{
    worldElement: {
      name: string;
      type: string;
      description: string;
      details: Record<string, unknown>;
      connections: string[];
      atmosphere: string;
      significance: string;
    };
  }>> {
    return this.withErrorHandling(async () => {
      const prompt = `
Create detailed world-building for a ${request.genre} story:

Type: ${request.type}
Name: ${request.name}
Description: ${request.description}

Generate comprehensive details including:
- Physical/conceptual characteristics
- Cultural or functional significance
- Historical context
- Rules or limitations (if applicable)
- Atmospheric elements
- Connections to other story elements
- Story significance and potential

Format as JSON with the worldElement structure.
      `;

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert world-builder. Create rich, internally consistent world elements that enhance storytelling.'
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 2000,
      });

      const content = completion.choices[0]?.message?.content || '{}';
      
      try {
        const worldElement = JSON.parse(content);
        return { worldElement };
      } catch {
        return {
          worldElement: {
            name: request.name,
            type: request.type,
            description: request.description,
            details: { raw: content },
            connections: [],
            atmosphere: 'To be determined',
            significance: 'To be determined'
          }
        };
      }
    });
  }

  private async processGenerationQueue(): Promise<void> {
    if (this.generationQueue.length === 0 || this.activeGenerations.size >= 3) {
      return; // No tasks or max concurrent generations reached
    }

    const task = this.generationQueue.shift();
    if (!task) return;

    task.status = 'processing';
    this.activeGenerations.set(task.id, { task, startTime: Date.now() });

    try {
      const result = await this.executeGeneration(task);
      const generationResult = result as GenerationResult;
      task.output = {
        content: String(generationResult.content || ''),
        metadata: generationResult.metadata || {},
        quality: Number(generationResult.quality || 0.8)
      };
      task.status = 'completed';
      task.completedAt = Date.now();
    } catch (error) {
      task.status = 'failed';
      console.error(`Generation failed for task ${task.id}:`, error);
    } finally {
      this.activeGenerations.delete(task.id);
    }
  }

  private async executeGeneration(task: WritingTask): Promise<Record<string, unknown>> {
    const context = task.input.context || {};
    const type = context.type || 'general';
    
    const systemPrompts = {
      scene: 'You are an expert scene writer. Create vivid, engaging scenes that advance plot and develop characters.',
      dialogue: 'You are a dialogue specialist. Write natural, character-revealing conversations.',
      description: 'You are a descriptive writer. Create immersive, sensory-rich descriptions.',
      chapter: 'You are a chapter writer. Structure compelling chapter content with proper pacing.',
      character: 'You are a character developer. Create complex, believable characters.',
      'plot-outline': 'You are a plot architect. Design engaging story structures and outlines.',
      general: 'You are a creative writing assistant. Adapt your style to the task at hand.'
    };

    const systemPrompt = systemPrompts[type as keyof typeof systemPrompts] || systemPrompts.general;

    let userPrompt = task.input.prompt || '';
    
    if (context.style) userPrompt += `\n\nStyle: ${context.style}`;
    if (context.tone) userPrompt += `\nTone: ${context.tone}`;
    if (context.length) userPrompt += `\nLength: ${context.length}`;

    const completion = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: type === 'dialogue' ? 0.8 : 0.7,
      max_tokens: context.length === 'long' ? 3000 : context.length === 'medium' ? 1500 : 1000,
    });

    const content = completion.choices[0]?.message?.content || '';
    
    return {
      content,
      metadata: {
        type: type,
        model: 'gpt-4',
        processingTime: Date.now() - (this.activeGenerations.get(task.id)?.startTime || Date.now()),
        tokensUsed: completion.usage?.total_tokens || 0,
        style: String(context.style || ''),
        tone: String(context.tone || '')
      },
      quality: this.assessGenerationQuality(content, String(type))
    };
  }

  private assessGenerationQuality(content: string, type: string): number {
    let score = 60; // Base score

    // Length appropriateness
    if (content.length > 200) score += 10;
    if (content.length > 1000) score += 5;

    // Structure check
    const paragraphs = content.split('\n\n').filter(p => p.trim());
    if (paragraphs.length > 1) score += 10;

    // Type-specific quality checks
    switch (type) {
      case 'dialogue':
        if (content.includes('"') && content.includes('said')) score += 10;
        break;
      case 'description':
        if (content.match(/\b(saw|heard|felt|smelled|tasted)\b/gi)) score += 10;
        break;
      case 'scene':
        if (content.includes('"') && paragraphs.length >= 3) score += 15;
        break;
    }

    // Readability check
    const sentences = content.split(/[.!?]+/).filter(s => s.trim());
    const avgSentenceLength = content.split(/\s+/).length / sentences.length;
    if (avgSentenceLength > 8 && avgSentenceLength < 30) score += 5;

    return Math.min(100, Math.max(30, score));
  }

  private startGenerationProcessor(): void {
    setInterval(() => {
      this.processGenerationQueue();
    }, 1000);
  }
}